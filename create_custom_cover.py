#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
定制版封面图生成器 - 专为用户需求设计
包含：软件开发、脚本定制、Windows软件开发、App小程序开发、嵌入式软件开发、STM32全系列开发、网站开发
"""

from PIL import Image, ImageDraw, ImageFont
import os
from datetime import datetime

def create_custom_cover():
    """创建定制封面图"""
    width, height = 1920, 1080
    
    # 创建深色科技背景
    img = Image.new('RGB', (width, height), color='#0A0E1A')
    draw = ImageDraw.Draw(img)
    
    # 创建科技感渐变背景
    for y in range(height):
        ratio = y / height
        r = int(10 + ratio * 40)   # 10 -> 50
        g = int(14 + ratio * 50)   # 14 -> 64
        b = int(26 + ratio * 80)   # 26 -> 106
        color = (r, g, b)
        draw.line([(0, y), (width, y)], fill=color)
    
    # 加载字体
    try:
        title_font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 64)
        subtitle_font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 32)
        tech_font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 24)
        small_font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 18)
    except:
        title_font = ImageFont.load_default()
        subtitle_font = ImageFont.load_default()
        tech_font = ImageFont.load_default()
        small_font = ImageFont.load_default()
    
    # 添加网格背景装饰
    grid_color = (100, 150, 200, 30)
    grid_size = 60
    for x in range(0, width, grid_size):
        draw.line([(x, 0), (x, height)], fill=grid_color, width=1)
    for y in range(0, height, grid_size):
        draw.line([(0, y), (width, y)], fill=grid_color, width=1)
    
    # 主标题
    main_title = "专业软件开发服务"
    title_bbox = draw.textbbox((0, 0), main_title, font=title_font)
    title_width = title_bbox[2] - title_bbox[0]
    title_x = (width - title_width) // 2
    
    # 标题背景效果
    draw.rounded_rectangle([title_x-40, 80, title_x+title_width+40, 150], 
                          radius=15, fill=(0, 100, 200, 180), outline=(100, 200, 255), width=3)
    draw.text((title_x, 100), main_title, fill='white', font=title_font)
    
    # 英文副标题
    subtitle = "Professional Software Development Services"
    subtitle_bbox = draw.textbbox((0, 0), subtitle, font=subtitle_font)
    subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
    subtitle_x = (width - subtitle_width) // 2
    draw.text((subtitle_x, 180), subtitle, fill='#B0C4DE', font=subtitle_font)
    
    # 核心技术领域 - 按您的要求排列
    tech_services = [
        ("软件开发 & 脚本定制", "Software Development & Script Customization", "#FF6B35", (200, 280)),
        ("Windows软件开发", "Windows Application Development", "#4169E1", (1000, 280)),
        ("网站开发", "Web Development", "#32CD32", (200, 400)),
        ("App & 小程序开发", "Mobile App & Mini-Program Development", "#FF1493", (1000, 400)),
        ("嵌入式软件开发", "Embedded Software Development", "#9932CC", (200, 520)),
        ("STM32全系列开发", "STM32 Series Development", "#FF8C00", (1000, 520))
    ]
    
    for chinese, english, color, (x, y) in tech_services:
        # 转换颜色
        if color.startswith('#'):
            color_rgb = tuple(int(color[i:i+2], 16) for i in (1, 3, 5))
        else:
            color_rgb = (100, 150, 200)
        
        # 服务卡片背景
        card_width = 600
        card_height = 80
        
        # 卡片阴影
        draw.rounded_rectangle([x+5, y+5, x+card_width+5, y+card_height+5], 
                              radius=12, fill=(0, 0, 0, 100))
        
        # 卡片主体
        draw.rounded_rectangle([x, y, x+card_width, y+card_height], 
                              radius=12, fill=color_rgb, outline='white', width=2)
        
        # 内部渐变效果
        for i in range(3):
            alpha = 50 - i * 15
            inner_color = tuple(list(color_rgb) + [alpha])
            draw.rounded_rectangle([x+i*2, y+i*2, x+card_width-i*2, y+card_height-i*2], 
                                  radius=12-i, outline=(255, 255, 255, alpha), width=1)
        
        # 文字
        draw.text((x+20, y+15), chinese, fill='white', font=tech_font)
        draw.text((x+20, y+45), english, fill='#E6E6FA', font=small_font)
    
    # 技术特色标签
    features = [
        "✓ 定制化解决方案", "✓ 专业技术团队", "✓ 快速交付", 
        "✓ 持续技术支持", "✓ 质量保证", "✓ 合理价格"
    ]
    
    feature_y = 650
    feature_spacing = width // len(features)
    
    for i, feature in enumerate(features):
        x_pos = feature_spacing * i + feature_spacing // 2
        feature_bbox = draw.textbbox((0, 0), feature, font=small_font)
        feature_width = feature_bbox[2] - feature_bbox[0]
        
        # 特色标签背景
        draw.rounded_rectangle([x_pos - feature_width//2 - 10, feature_y - 5, 
                               x_pos + feature_width//2 + 10, feature_y + 25], 
                              radius=8, fill=(0, 50, 100, 150), outline=(100, 200, 255), width=1)
        
        draw.text((x_pos - feature_width//2, feature_y), feature, fill='#87CEEB', font=small_font)
    
    # 联系信息区域
    contact_y = 750
    draw.rounded_rectangle([100, contact_y, width-100, contact_y+80], 
                          radius=15, fill=(20, 30, 50, 200), outline=(100, 200, 255), width=2)
    
    # 联系信息文字
    contact_title = "🚀 专业开发团队，为您提供一站式软件解决方案"
    contact_bbox = draw.textbbox((0, 0), contact_title, font=subtitle_font)
    contact_width = contact_bbox[2] - contact_bbox[0]
    contact_x = (width - contact_width) // 2
    
    draw.text((contact_x, contact_y + 15), contact_title, fill='white', font=subtitle_font)
    
    # 底部时间和版权信息
    current_time = datetime.now().strftime("%Y年%m月")
    footer_text = f"© {current_time} 专业软件开发服务 | 技术驱动创新 | 品质成就未来"
    footer_bbox = draw.textbbox((0, 0), footer_text, font=small_font)
    footer_width = footer_bbox[2] - footer_bbox[0]
    footer_x = (width - footer_width) // 2
    
    draw.text((footer_x, height - 60), footer_text, fill='#708090', font=small_font)
    
    # 添加装饰性元素
    # 左上角科技装饰
    draw.polygon([(0, 0), (150, 0), (0, 150)], fill=(0, 100, 200, 50))
    draw.polygon([(0, 0), (100, 0), (0, 100)], fill=(100, 200, 255, 30))
    
    # 右下角科技装饰
    draw.polygon([(width, height), (width-150, height), (width, height-150)], fill=(200, 100, 0, 50))
    draw.polygon([(width, height), (width-100, height), (width, height-100)], fill=(255, 150, 50, 30))
    
    # 添加一些科技感的圆点
    dot_positions = [
        (150, 250), (1770, 250), (150, 650), (1770, 650),
        (width//2, 350), (width//2, 550)
    ]
    
    for x, y in dot_positions:
        draw.ellipse([x-15, y-15, x+15, y+15], fill=(100, 200, 255), outline='white', width=2)
        draw.ellipse([x-8, y-8, x+8, y+8], fill=(255, 255, 255, 150))
    
    # 保存图片
    output_path = 'custom_software_cover.png'
    img.save(output_path, 'PNG', quality=95)
    
    print(f"定制封面图已生成: {output_path}")
    print(f"图片尺寸: {width}x{height} 像素 (16:9比例)")
    print("\n包含的技术服务领域:")
    for chinese, english, _, _ in tech_services:
        print(f"  • {chinese}")
        print(f"    {english}")
    
    print(f"\n特色功能:")
    for feature in features:
        print(f"  {feature}")
    
    return output_path

if __name__ == "__main__":
    try:
        cover_path = create_custom_cover()
        print(f"\n✅ 定制封面图创建成功!")
        print(f"📁 文件位置: {os.path.abspath(cover_path)}")
        print("\n🎨 设计特点:")
        print("  • 专业深色科技背景")
        print("  • 清晰的服务分类展示")
        print("  • 中英文双语标识")
        print("  • 现代化卡片设计")
        print("  • 完整的服务特色说明")
        print("  • 适合商业使用的专业外观")
    except Exception as e:
        print(f"❌ 创建封面图时出错: {e}")
        import traceback
        traceback.print_exc()
