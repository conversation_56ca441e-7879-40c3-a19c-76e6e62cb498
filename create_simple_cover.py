#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版封面图生成器 - 软件开发主题
使用PIL创建专业的软件开发封面图
"""

from PIL import Image, ImageDraw, ImageFont
import os
from datetime import datetime

def create_cover_image():
    """创建封面图"""
    # 创建画布 - 16:9比例，高分辨率
    width, height = 1920, 1080
    
    # 创建渐变背景
    img = Image.new('RGB', (width, height), color='#0F172A')
    draw = ImageDraw.Draw(img)
    
    # 创建渐变效果
    for y in range(height):
        # 从深蓝到稍浅的蓝色渐变
        ratio = y / height
        r = int(15 + ratio * 30)   # 15 -> 45
        g = int(23 + ratio * 40)   # 23 -> 63  
        b = int(42 + ratio * 60)   # 42 -> 102
        color = (r, g, b)
        draw.line([(0, y), (width, y)], fill=color)
    
    # 尝试加载字体，如果失败则使用默认字体
    try:
        # 尝试不同的字体
        title_font = ImageFont.truetype("arial.ttf", 72)
        subtitle_font = ImageFont.truetype("arial.ttf", 36)
        tech_font = ImageFont.truetype("arial.ttf", 28)
        small_font = ImageFont.truetype("arial.ttf", 20)
    except:
        try:
            title_font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 72)
            subtitle_font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 36)
            tech_font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 28)
            small_font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 20)
        except:
            # 使用默认字体
            title_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()
            tech_font = ImageFont.load_default()
            small_font = ImageFont.load_default()
    
    # 添加装饰元素
    # 左上角圆形
    draw.ellipse([100, 100, 250, 250], fill=(0, 212, 255, 80))
    
    # 右下角矩形
    draw.rounded_rectangle([width-300, height-200, width-50, height-50], 
                          radius=20, fill=(255, 107, 53, 60))
    
    # 添加一些装饰线条
    for i in range(8):
        y_pos = 300 + i * 60
        alpha = 30 - i * 3
        draw.line([(0, y_pos), (width, y_pos)], fill=(255, 255, 255, alpha), width=2)
    
    # 主标题
    title_text = "Software Development"
    title_bbox = draw.textbbox((0, 0), title_text, font=title_font)
    title_width = title_bbox[2] - title_bbox[0]
    title_x = (width - title_width) // 2
    
    # 标题背景
    draw.rounded_rectangle([title_x-40, 180, title_x+title_width+40, 260], 
                          radius=15, fill=(30, 58, 138, 200))
    
    # 标题文字
    draw.text((title_x, 200), title_text, fill='white', font=title_font)
    
    # 中文副标题
    subtitle_text = "软件开发 & 技术定制服务"
    subtitle_bbox = draw.textbbox((0, 0), subtitle_text, font=subtitle_font)
    subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
    subtitle_x = (width - subtitle_width) // 2
    draw.text((subtitle_x, 300), subtitle_text, fill='#E5E7EB', font=subtitle_font)
    
    # 技术领域标签
    technologies = [
        ("Windows Software", "#4F46E5", (200, 450)),
        ("Web Development", "#059669", (600, 420)),
        ("Mobile Apps", "#DC2626", (1000, 450)),
        ("Embedded Dev", "#7C3AED", (200, 580)),
        ("STM32 Series", "#EA580C", (600, 610)),
        ("Script Custom", "#0891B2", (1000, 580))
    ]
    
    for tech, color, (x, y) in technologies:
        # 技术标签背景
        tech_bbox = draw.textbbox((0, 0), tech, font=tech_font)
        tech_width = tech_bbox[2] - tech_bbox[0]
        tech_height = tech_bbox[3] - tech_bbox[1]
        
        # 转换颜色字符串为RGB
        color_rgb = tuple(int(color[i:i+2], 16) for i in (1, 3, 5))
        
        draw.rounded_rectangle([x-20, y-15, x+tech_width+20, y+tech_height+15], 
                              radius=10, fill=color_rgb, outline='white', width=3)
        
        # 技术名称
        draw.text((x, y), tech, fill='white', font=tech_font)
    
    # 添加代码装饰元素
    code_elements = [
        ("def develop():", 100, 750, "#00FF88"),
        ("    return 'success'", 120, 780, "#00FF88"),
        ("<html>", width-200, 750, "#FF6B35"),
        ("</html>", width-180, 780, "#FF6B35"),
        ("while(1) {", 100, 150, "#FFD700"),
        ("  // STM32", 120, 120, "#FFD700")
    ]
    
    for code, x, y, color_hex in code_elements:
        color_rgb = tuple(int(color_hex[i:i+2], 16) for i in (1, 3, 5))
        draw.text((x, y), code, fill=color_rgb, font=small_font)
    
    # 底部信息
    current_time = datetime.now().strftime("%Y年%m月")
    footer_text = f"Professional Development Team | {current_time}"
    footer_bbox = draw.textbbox((0, 0), footer_text, font=small_font)
    footer_width = footer_bbox[2] - footer_bbox[0]
    footer_x = (width - footer_width) // 2
    draw.text((footer_x, height-80), footer_text, fill='#9CA3AF', font=small_font)
    
    # 添加技术符号
    symbols = ['⚡', '🔧', '💻', '📱', '🌐', '⚙️']
    symbol_spacing = width // (len(symbols) + 1)
    for i, symbol in enumerate(symbols):
        x_pos = symbol_spacing * (i + 1)
        try:
            draw.text((x_pos, height-40), symbol, fill='#6B7280', font=small_font)
        except:
            # 如果符号不支持，用文字代替
            replacements = ['POWER', 'TOOLS', 'PC', 'MOBILE', 'WEB', 'GEAR']
            draw.text((x_pos, height-40), replacements[i], fill='#6B7280', font=small_font)
    
    # 保存图片
    output_path = 'software_development_cover.png'
    img.save(output_path, 'PNG', quality=95)
    
    print(f"封面图已生成: {output_path}")
    print(f"图片尺寸: {width}x{height} 像素 (16:9比例)")
    print("包含技术领域:")
    for tech, _, _ in technologies:
        print(f"  • {tech}")
    
    return output_path

if __name__ == "__main__":
    try:
        cover_path = create_cover_image()
        print(f"\n✅ 封面图创建成功!")
        print(f"📁 文件位置: {os.path.abspath(cover_path)}")
    except Exception as e:
        print(f"❌ 创建封面图时出错: {e}")
        import traceback
        traceback.print_exc()
