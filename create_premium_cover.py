#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级版封面图生成器 - 软件开发主题
创建更加精美的软件开发封面图，包含更多视觉效果
"""

from PIL import Image, ImageDraw, ImageFont, ImageFilter
import math
import os
from datetime import datetime

def create_gradient_background(width, height):
    """创建复杂渐变背景"""
    img = Image.new('RGB', (width, height))
    draw = ImageDraw.Draw(img)
    
    # 创建多层渐变效果
    for y in range(height):
        for x in range(width):
            # 计算距离中心的距离
            center_x, center_y = width // 2, height // 2
            distance = math.sqrt((x - center_x)**2 + (y - center_y)**2)
            max_distance = math.sqrt(center_x**2 + center_y**2)
            
            # 基础渐变
            ratio_y = y / height
            ratio_x = x / width
            ratio_center = distance / max_distance
            
            # 多层颜色混合
            r = int(15 + ratio_y * 25 + ratio_center * 20)
            g = int(23 + ratio_y * 35 + ratio_x * 15)
            b = int(42 + ratio_y * 50 + ratio_center * 30)
            
            # 限制颜色范围
            r = min(max(r, 0), 100)
            g = min(max(g, 0), 120)
            b = min(max(b, 0), 150)
            
            img.putpixel((x, y), (r, g, b))
    
    return img

def add_tech_icons(draw, width, height, font):
    """添加技术图标和装饰"""
    # 添加电路板风格的线条
    line_color = (100, 200, 255, 100)
    
    # 水平线条
    for i in range(0, width, 150):
        draw.line([(i, height//3), (i+100, height//3)], fill=line_color, width=2)
        draw.line([(i, 2*height//3), (i+80, 2*height//3)], fill=line_color, width=2)
    
    # 垂直线条
    for i in range(0, height, 120):
        draw.line([(width//4, i), (width//4, i+60)], fill=line_color, width=2)
        draw.line([(3*width//4, i), (3*width//4, i+40)], fill=line_color, width=2)
    
    # 添加节点圆圈
    node_positions = [
        (width//4, height//3), (3*width//4, height//3),
        (width//4, 2*height//3), (3*width//4, 2*height//3),
        (width//2, height//2)
    ]
    
    for x, y in node_positions:
        draw.ellipse([x-8, y-8, x+8, y+8], fill=(0, 255, 200), outline=(255, 255, 255), width=2)

def create_premium_cover():
    """创建高级封面图"""
    width, height = 1920, 1080
    
    # 创建复杂背景
    img = create_gradient_background(width, height)
    draw = ImageDraw.Draw(img)
    
    # 加载字体
    try:
        title_font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 80)
        subtitle_font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 40)
        tech_font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 32)
        small_font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 24)
        code_font = ImageFont.truetype("C:/Windows/Fonts/consola.ttf", 18)
    except:
        title_font = ImageFont.load_default()
        subtitle_font = ImageFont.load_default()
        tech_font = ImageFont.load_default()
        small_font = ImageFont.load_default()
        code_font = ImageFont.load_default()
    
    # 添加技术装饰
    add_tech_icons(draw, width, height, small_font)
    
    # 主标题区域
    title_text = "SOFTWARE DEVELOPMENT"
    title_bbox = draw.textbbox((0, 0), title_text, font=title_font)
    title_width = title_bbox[2] - title_bbox[0]
    title_x = (width - title_width) // 2
    
    # 标题发光效果背景
    for offset in range(10, 0, -1):
        alpha = 20 + offset * 5
        glow_color = (30, 58, 138, alpha)
        draw.rounded_rectangle([title_x-50-offset, 150-offset, title_x+title_width+50+offset, 240+offset], 
                              radius=20+offset, fill=glow_color)
    
    # 标题主背景
    draw.rounded_rectangle([title_x-50, 150, title_x+title_width+50, 240], 
                          radius=20, fill=(30, 58, 138, 220), outline=(100, 200, 255), width=3)
    
    # 标题文字
    draw.text((title_x, 170), title_text, fill='white', font=title_font)
    
    # 中文副标题
    subtitle_text = "专业软件开发 · 技术定制服务"
    subtitle_bbox = draw.textbbox((0, 0), subtitle_text, font=subtitle_font)
    subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
    subtitle_x = (width - subtitle_width) // 2
    
    # 副标题背景
    draw.rounded_rectangle([subtitle_x-30, 270, subtitle_x+subtitle_width+30, 320], 
                          radius=15, fill=(0, 0, 0, 100), outline=(200, 200, 200), width=2)
    draw.text((subtitle_x, 280), subtitle_text, fill='#E5E7EB', font=subtitle_font)
    
    # 技术领域卡片
    tech_areas = [
        ("Windows软件开发", "Desktop Applications", "#4F46E5", (160, 400)),
        ("网站开发", "Web Development", "#059669", (640, 380)),
        ("App小程序开发", "Mobile Applications", "#DC2626", (1120, 400)),
        ("嵌入式开发", "Embedded Systems", "#7C3AED", (160, 580)),
        ("STM32全系列", "STM32 Microcontrollers", "#EA580C", (640, 600)),
        ("脚本定制", "Custom Scripts", "#0891B2", (1120, 580))
    ]
    
    for chinese, english, color, (x, y) in tech_areas:
        # 转换颜色
        color_rgb = tuple(int(color[i:i+2], 16) for i in (1, 3, 5))
        
        # 卡片阴影
        shadow_offset = 5
        draw.rounded_rectangle([x-25+shadow_offset, y-25+shadow_offset, x+275+shadow_offset, y+85+shadow_offset], 
                              radius=15, fill=(0, 0, 0, 50))
        
        # 卡片背景
        draw.rounded_rectangle([x-25, y-25, x+275, y+85], 
                              radius=15, fill=color_rgb, outline='white', width=3)
        
        # 渐变效果
        for i in range(5):
            alpha = 30 - i * 5
            draw.rounded_rectangle([x-25+i, y-25+i, x+275-i, y+85-i], 
                                  radius=15-i, outline=(255, 255, 255, alpha), width=1)
        
        # 文字
        draw.text((x, y-5), chinese, fill='white', font=tech_font)
        draw.text((x, y+25), english, fill='#E5E7EB', font=small_font)
    
    # 代码装饰区域
    code_snippets = [
        ("// Windows Development", "HWND hwnd = CreateWindow(...);", 50, 800),
        ("<!-- Web Development -->", "<div class='container'>", 50, 850),
        ("# Python Scripts", "def automate_task():", 50, 900),
        ("/* Embedded C */", "while(1) { HAL_GPIO_Toggle(); }", width-400, 800),
        ("// STM32 HAL", "HAL_UART_Transmit(&huart1, data, len);", width-400, 850),
        ("// Mobile App", "Intent intent = new Intent(this, Activity.class);", width-400, 900)
    ]
    
    for comment, code, x, y in code_snippets:
        # 代码背景
        draw.rounded_rectangle([x-10, y-10, x+350, y+40], 
                              radius=8, fill=(0, 0, 0, 120), outline=(100, 255, 100, 100), width=1)
        
        # 代码文字
        draw.text((x, y), comment, fill='#00FF88', font=code_font)
        draw.text((x, y+20), code, fill='#FFD700', font=code_font)
    
    # 底部信息栏
    current_time = datetime.now().strftime("%Y年%m月")
    footer_bg_y = height - 100
    draw.rounded_rectangle([50, footer_bg_y, width-50, height-20], 
                          radius=10, fill=(0, 0, 0, 150), outline=(100, 200, 255), width=2)
    
    # 底部文字
    footer_left = f"Professional Development Team | {current_time}"
    footer_right = "Custom Solutions · Quality Code · Reliable Service"
    
    draw.text((80, footer_bg_y+20), footer_left, fill='#9CA3AF', font=small_font)
    
    footer_right_bbox = draw.textbbox((0, 0), footer_right, font=small_font)
    footer_right_width = footer_right_bbox[2] - footer_right_bbox[0]
    draw.text((width-80-footer_right_width, footer_bg_y+20), footer_right, fill='#9CA3AF', font=small_font)
    
    # 添加装饰性几何图形
    # 左上角
    draw.polygon([(0, 0), (200, 0), (0, 200)], fill=(100, 200, 255, 30))
    # 右下角
    draw.polygon([(width, height), (width-200, height), (width, height-200)], fill=(255, 100, 100, 30))
    
    # 保存图片
    output_path = 'premium_software_cover.png'
    img.save(output_path, 'PNG', quality=95)
    
    print(f"高级封面图已生成: {output_path}")
    print(f"图片尺寸: {width}x{height} 像素 (16:9比例)")
    print("包含技术领域:")
    for chinese, english, _, _ in tech_areas:
        print(f"  • {chinese} ({english})")
    
    return output_path

if __name__ == "__main__":
    try:
        cover_path = create_premium_cover()
        print(f"\n✅ 高级封面图创建成功!")
        print(f"📁 文件位置: {os.path.abspath(cover_path)}")
        print("\n🎨 特色功能:")
        print("  • 复杂渐变背景")
        print("  • 电路板风格装饰")
        print("  • 发光效果标题")
        print("  • 3D卡片样式")
        print("  • 代码片段装饰")
        print("  • 专业配色方案")
    except Exception as e:
        print(f"❌ 创建封面图时出错: {e}")
        import traceback
        traceback.print_exc()
