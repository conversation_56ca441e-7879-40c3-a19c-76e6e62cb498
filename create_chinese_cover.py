#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文封面图生成器 - 修复中文显示问题
专门处理中文字体，确保中文正确显示
"""

from PIL import Image, ImageDraw, ImageFont
import os
from datetime import datetime

def get_chinese_font(size):
    """获取支持中文的字体"""
    font_paths = [
        "C:/Windows/Fonts/msyh.ttc",      # 微软雅黑
        "C:/Windows/Fonts/simhei.ttf",    # 黑体
        "C:/Windows/Fonts/simsun.ttc",    # 宋体
        "C:/Windows/Fonts/simkai.ttf",    # 楷体
        "/System/Library/Fonts/PingFang.ttc",  # Mac 苹方
        "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"  # Linux
    ]
    
    for font_path in font_paths:
        try:
            if os.path.exists(font_path):
                return ImageFont.truetype(font_path, size)
        except:
            continue
    
    # 如果都失败了，尝试系统默认字体
    try:
        return ImageFont.truetype("arial.ttf", size)
    except:
        return ImageFont.load_default()

def create_chinese_cover():
    """创建支持中文的封面图"""
    width, height = 800, 800
    
    # 创建深蓝色科技背景
    img = Image.new('RGB', (width, height), color='#0B1426')
    draw = ImageDraw.Draw(img)
    
    # 创建渐变背景
    for y in range(height):
        ratio = y / height
        r = int(11 + ratio * 35)   # 11 -> 46
        g = int(20 + ratio * 45)   # 20 -> 65
        b = int(38 + ratio * 70)   # 38 -> 108
        color = (r, g, b)
        draw.line([(0, y), (width, y)], fill=color)
    
    # 获取中文字体 - 调整为正方形尺寸
    title_font = get_chinese_font(36)
    subtitle_font = get_chinese_font(18)
    tech_font = get_chinese_font(16)
    small_font = get_chinese_font(12)
    
    print("字体加载完成，开始绘制...")
    
    # 添加网格背景
    grid_color = (80, 120, 160, 40)
    grid_size = 40
    for x in range(0, width, grid_size):
        draw.line([(x, 0), (x, height)], fill=grid_color[:3], width=1)
    for y in range(0, height, grid_size):
        draw.line([(0, y), (width, y)], fill=grid_color[:3], width=1)
    
    # 主标题
    main_title = "专业软件开发服务"
    title_bbox = draw.textbbox((0, 0), main_title, font=title_font)
    title_width = title_bbox[2] - title_bbox[0]
    title_x = (width - title_width) // 2
    
    # 标题背景
    draw.rounded_rectangle([title_x-20, 40, title_x+title_width+20, 85],
                          radius=10, fill=(30, 80, 150, 200), outline=(100, 180, 255), width=2)
    draw.text((title_x, 50), main_title, fill='white', font=title_font)

    # 英文副标题
    subtitle = "Professional Software Development"
    subtitle_bbox = draw.textbbox((0, 0), subtitle, font=subtitle_font)
    subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
    subtitle_x = (width - subtitle_width) // 2
    draw.text((subtitle_x, 100), subtitle, fill='#B0C4DE', font=subtitle_font)
    
    # 技术服务卡片 - 使用纯中文，重新布局为正方形
    tech_services = [
        ("软件开发", "#FF6B35", (80, 150)),
        ("脚本定制", "#4169E1", (420, 150)),
        ("Windows软件开发", "#32CD32", (80, 220)),
        ("网站开发", "#FF1493", (420, 220)),
        ("App小程序开发", "#9932CC", (80, 290)),
        ("嵌入式开发", "#FF8C00", (420, 290)),
        ("STM32全系列开发", "#DC143C", (250, 360))
    ]
    
    for service, color, (x, y) in tech_services:
        # 转换颜色
        color_rgb = tuple(int(color[i:i+2], 16) for i in (1, 3, 5))
        
        # 计算卡片尺寸
        text_bbox = draw.textbbox((0, 0), service, font=tech_font)
        text_width = text_bbox[2] - text_bbox[0]
        card_width = text_width + 30
        card_height = 40
        
        # 卡片阴影
        draw.rounded_rectangle([x+3, y+3, x+card_width+3, y+card_height+3], 
                              radius=10, fill=(0, 0, 0, 80))
        
        # 卡片主体
        draw.rounded_rectangle([x, y, x+card_width, y+card_height], 
                              radius=10, fill=color_rgb, outline='white', width=2)
        
        # 文字居中
        text_x = x + (card_width - text_width) // 2
        text_y = y + (card_height - (text_bbox[3] - text_bbox[1])) // 2
        draw.text((text_x, text_y), service, fill='white', font=tech_font)
    
    # 服务特色 - 重新布局为两行
    features = [
        "✓ 专业团队", "✓ 快速交付", "✓ 质量保证",
        "✓ 技术支持", "✓ 合理价格", "✓ 定制方案"
    ]

    # 第一行特色
    feature_y1 = 450
    for i in range(3):
        feature = features[i]
        x_pos = 130 + i * 180

        feature_bbox = draw.textbbox((0, 0), feature, font=small_font)
        feature_width = feature_bbox[2] - feature_bbox[0]

        draw.rounded_rectangle([x_pos - feature_width//2 - 6, feature_y1 - 2,
                               x_pos + feature_width//2 + 6, feature_y1 + 18],
                              radius=5, fill=(0, 60, 120, 150), outline=(100, 200, 255), width=1)

        draw.text((x_pos - feature_width//2, feature_y1), feature, fill='#87CEEB', font=small_font)

    # 第二行特色
    feature_y2 = 480
    for i in range(3, 6):
        feature = features[i]
        x_pos = 130 + (i-3) * 180

        feature_bbox = draw.textbbox((0, 0), feature, font=small_font)
        feature_width = feature_bbox[2] - feature_bbox[0]

        draw.rounded_rectangle([x_pos - feature_width//2 - 6, feature_y2 - 2,
                               x_pos + feature_width//2 + 6, feature_y2 + 18],
                              radius=5, fill=(0, 60, 120, 150), outline=(100, 200, 255), width=1)

        draw.text((x_pos - feature_width//2, feature_y2), feature, fill='#87CEEB', font=small_font)
    
    # 联系信息区域
    contact_y = 550
    contact_text = "专业开发团队 · 一站式软件解决方案"
    contact_bbox = draw.textbbox((0, 0), contact_text, font=subtitle_font)
    contact_width = contact_bbox[2] - contact_bbox[0]
    contact_x = (width - contact_width) // 2

    # 联系信息背景
    draw.rounded_rectangle([contact_x-20, contact_y-8, contact_x+contact_width+20, contact_y+30],
                          radius=10, fill=(20, 40, 80, 180), outline=(100, 200, 255), width=2)

    draw.text((contact_x, contact_y), contact_text, fill='white', font=subtitle_font)

    # 底部信息
    current_time = datetime.now().strftime("%Y年%m月")
    footer_text = f"© {current_time} 专业软件开发服务"
    footer_bbox = draw.textbbox((0, 0), footer_text, font=small_font)
    footer_width = footer_bbox[2] - footer_bbox[0]
    footer_x = (width - footer_width) // 2

    draw.text((footer_x, height - 40), footer_text, fill='#708090', font=small_font)
    
    # 装饰元素
    # 左上角
    draw.polygon([(0, 0), (80, 0), (0, 80)], fill=(0, 100, 200, 60))

    # 右下角
    draw.polygon([(width, height), (width-80, height), (width, height-80)], fill=(200, 100, 0, 60))

    # 科技圆点
    dot_positions = [(100, 650), (700, 650), (100, 720), (700, 720)]

    for x, y in dot_positions:
        draw.ellipse([x-8, y-8, x+8, y+8], fill=(100, 200, 255), outline='white', width=2)
        draw.ellipse([x-4, y-4, x+4, y+4], fill='white')
    
    # 保存图片
    output_path = 'chinese_software_cover_800x800.png'
    img.save(output_path, 'PNG', quality=95)
    
    print(f"中文封面图已生成: {output_path}")
    print(f"图片尺寸: {width}x{height} 像素")
    print("\n包含的技术服务:")
    for service, _, _ in tech_services:
        print(f"  • {service}")
    
    return output_path

if __name__ == "__main__":
    try:
        print("开始创建中文封面图...")
        cover_path = create_chinese_cover()
        print(f"\n✅ 中文封面图创建成功!")
        print(f"📁 文件位置: {os.path.abspath(cover_path)}")
        print("\n🎨 修复内容:")
        print("  • 修复中文字体显示问题")
        print("  • 使用系统中文字体")
        print("  • 纯中文技术标签")
        print("  • 专业深色科技背景")
        print("  • 清晰的服务分类")
    except Exception as e:
        print(f"❌ 创建封面图时出错: {e}")
        import traceback
        traceback.print_exc()
